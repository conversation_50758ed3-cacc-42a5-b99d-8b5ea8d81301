package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.impl.DefaultCamelContext;
import org.apache.camel.support.DefaultExchange;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.exception.ApplicationException;

@ExtendWith(MockitoExtension.class)
class EthosColleagueInstructorByIdAssocResolverTest {

    @Mock
    private ProducerTemplate producerTemplate;

    @Mock
    private CamelContext camelContext;

    @Mock
    private EllucianEthosReadEndpointPaginator readEndpointPaginator;

    private EthosColleagueInstructorByIdAssocResolver resolver;
    private Exchange exchange;

    @BeforeEach
    void setUp() {
        resolver = new EthosColleagueInstructorByIdAssocResolver(producerTemplate, camelContext, readEndpointPaginator) {
            protected List<Map<String, ?>> invokeEthosReadEndpoint(ColleagueEthosAPIResource resource, Exchange exchange) {
                // Mock implementation for testing
                return mockInstructorResponse;
            }
        };
        exchange = new DefaultExchange(new DefaultCamelContext());
    }

    private List<Map<String, ?>> mockInstructorResponse;

    @Test
    void testProcessWithValidInstructor() throws Exception {
        // Given
        String personId = "8c005e2e-14fd-47f3-a772-eb88705818ef";
        exchange.setProperty("resourceId", personId);

        // Mock instructor response with matching person ID
        mockInstructorResponse = List.of(
            Map.of("instructor", Map.of("id", personId))
        );

        // When
        resolver.process(exchange);

        // Then
        List<Map<String, ?>> instructorData = (List<Map<String, ?>>) exchange.getProperty("ETHOS_ASSOC_INSTRUCTOR");
        assertNotNull(instructorData);
        assertEquals(1, instructorData.size());
        assertEquals(personId, ((Map<String, ?>) instructorData.get(0).get("instructor")).get("id"));
    }

    @Test
    void testProcessWithNonExistentInstructor() {
        // Given
        String personId = "8c005e2e-14fd-47f3-a772-eb88705818ef";
        exchange.setProperty("resourceId", personId);

        // Mock empty instructor response
        mockInstructorResponse = Collections.emptyList();

        // When & Then
        ApplicationException exception = assertThrows(ApplicationException.class, () -> {
            resolver.process(exchange);
        });

        assertEquals("No instructor was found for id '8c005e2e-14fd-47f3-a772-eb88705818ef'", exception.getMessage());
        assertEquals(404, exception.getStatusCode());
    }

    @Test
    void testProcessWithInstructorNotMatchingPersonId() {
        // Given
        String personId = "8c005e2e-14fd-47f3-a772-eb88705818ef";
        String differentPersonId = "different-person-id";
        exchange.setProperty("resourceId", personId);

        // Mock instructor response with different person ID
        mockInstructorResponse = List.of(
            Map.of("instructor", Map.of("id", differentPersonId))
        );

        // When & Then
        ApplicationException exception = assertThrows(ApplicationException.class, () -> {
            resolver.process(exchange);
        });

        assertEquals("No instructor was found for id '8c005e2e-14fd-47f3-a772-eb88705818ef'", exception.getMessage());
        assertEquals(404, exception.getStatusCode());
    }

    @Test
    void testProcessWithNullPersonId() {
        // Given
        exchange.setProperty("resourceId", (String) null);

        // When & Then
        ApplicationException exception = assertThrows(ApplicationException.class, () -> {
            resolver.process(exchange);
        });

        assertEquals("Person ID is required", exception.getMessage());
        assertEquals(400, exception.getStatusCode());
    }

    @Test
    void testProcessWithEmptyPersonId() {
        // Given
        exchange.setProperty("resourceId", "");

        // When & Then
        ApplicationException exception = assertThrows(ApplicationException.class, () -> {
            resolver.process(exchange);
        });

        assertEquals("Person ID is required", exception.getMessage());
        assertEquals(400, exception.getStatusCode());
    }

    @Test
    void testProcessWithWhitespacePersonId() {
        // Given
        exchange.setProperty("resourceId", "   ");

        // When & Then
        ApplicationException exception = assertThrows(ApplicationException.class, () -> {
            resolver.process(exchange);
        });

        assertEquals("Person ID is required", exception.getMessage());
        assertEquals(400, exception.getStatusCode());
    }

    @Test
    void testProcessWithMultipleInstructorRecords() throws Exception {
        // Given
        String personId = "8c005e2e-14fd-47f3-a772-eb88705818ef";
        exchange.setProperty("resourceId", personId);

        // Mock instructor response with multiple records for the same person
        mockInstructorResponse = List.of(
            Map.of("instructor", Map.of("id", personId)),
            Map.of("instructor", Map.of("id", personId))
        );

        // When
        resolver.process(exchange);

        // Then
        List<Map<String, ?>> instructorData = (List<Map<String, ?>>) exchange.getProperty("ETHOS_ASSOC_INSTRUCTOR");
        assertNotNull(instructorData);
        assertEquals(2, instructorData.size());
    }

    @Test
    void testProcessWithMixedInstructorRecords() throws Exception {
        // Given
        String personId = "8c005e2e-14fd-47f3-a772-eb88705818ef";
        String otherPersonId = "other-person-id";
        exchange.setProperty("resourceId", personId);

        // Mock instructor response with mixed person IDs
        mockInstructorResponse = List.of(
            Map.of("instructor", Map.of("id", personId)),
            Map.of("instructor", Map.of("id", otherPersonId)),
            Map.of("instructor", Map.of("id", personId))
        );

        // When
        resolver.process(exchange);

        // Then
        List<Map<String, ?>> instructorData = (List<Map<String, ?>>) exchange.getProperty("ETHOS_ASSOC_INSTRUCTOR");
        assertNotNull(instructorData);
        assertEquals(2, instructorData.size()); // Only records matching the requested person ID
        
        // Verify all returned records match the requested person ID
        for (Map<String, ?> instructor : instructorData) {
            assertEquals(personId, ((Map<String, ?>) instructor.get("instructor")).get("id"));
        }
    }

    @Test
    void testExtractPersonIdFromCamelHttpPath() throws Exception {
        // Given
        String personId = "8c005e2e-14fd-47f3-a772-eb88705818ef";
        exchange.getIn().setHeader("CamelHttpPath", "/uapi/integration/v1/instructors/" + personId);

        // Mock instructor response
        mockInstructorResponse = List.of(
            Map.of("instructor", Map.of("id", personId))
        );

        // When
        resolver.process(exchange);

        // Then
        List<Map<String, ?>> instructorData = (List<Map<String, ?>>) exchange.getProperty("ETHOS_ASSOC_INSTRUCTOR");
        assertNotNull(instructorData);
        assertEquals(1, instructorData.size());
    }
}
