package org.moderncampus.integration.colleague.workflow.route.identifier;

import static org.moderncampus.integration.Constants.COLLEAGUE_SYSTEM_ID;
import static org.moderncampus.integration.Constants.VERSION_1;
import static org.moderncampus.integration.route.identifier.RouteIdSupport.generateRouteId;

import org.moderncampus.integration.route.identifier.Constants;
import org.moderncampus.integration.route.identifier.IRouteId;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum CoreColleagueRouteIds implements IRouteId {

    V1_COLLEAGUE_CREATE_COURSE(Constants.CREATE_COURSE),
    V1_COLLEAGUE_CREATE_SECTION(Constants.CREATE_SECTION),
    V1_COLLEAGUE_UPDATE_COURSE(Constants.UPDATE_COURSE),
    V1_COLLEAGUE_GET_COURSE(Constants.GET_COURSE),
    V1_COLLEAGUE_CREATE_SECTION_INSTRUCTOR_ASSIGNMENT(Constants.CREATE_SECTION_INSTRUCTOR_ASSIGNMENT),
    V1_COLLEAGUE_CREATE_SECTION_SCHEDULE(Constants.CREATE_SECTION_SCHEDULE),
    V1_COLLEAGUE_CREATE_SECTION_CROSS_LIST(Constants.CREATE_SECTION_CROSS_LIST),
    V1_COLLEAGUE_DELETE_SECTION_SCHEDULE(Constants.DELETE_SECTION_SCHEDULE),
    V1_COLLEAGUE_DELETE_SECTION_CROSS_LIST(Constants.DELETE_SECTION_CROSS_LIST),
    V1_COLLEAGUE_GET_SECTION_SCHEDULES(Constants.GET_SECTION_SCHEDULES),
    V1_COLLEAGUE_GET_SECTION_CROSS_LISTS(Constants.GET_SECTION_CROSS_LISTS),
    V1_COLLEAGUE_DELETE_SECTION_INSTRUCTOR_ASSIGNMENT(Constants.DELETE_SECTION_INSTRUCTOR_ASSIGNMENT),
    V1_COLLEAGUE_GET_SECTION_INSTRUCTOR_ASSIGNMENTS(Constants.GET_SECTION_INSTRUCTOR_ASSIGNMENTS),
    V1_COLLEAGUE_UPDATE_SECTION(Constants.UPDATE_SECTION),
    V1_COLLEAGUE_GET_ADDRESS(Constants.GET_ADDRESS),
    V1_COLLEAGUE_CREATE_PERSON_EMERGENCY_CONTACT(Constants.CREATE_PERSON_EMERGENCY_CONTACT),
    V1_COLLEAGUE_DELETE_PERSON_EMERGENCY_CONTACT(Constants.DELETE_PERSON_EMERGENCY_CONTACT),
    V1_COLLEAGUE_CREATE_PERSON(Constants.CREATE_PERSON),
    V1_COLLEAGUE_CHECK_DUPLICATE_PERSON(Constants.CHECK_DUPLICATE_PERSON),
    V1_COLLEAGUE_CREATE_STUDENT_CHARGE(Constants.CREATE_STUDENT_CHARGE),
    V1_COLLEAGUE_CREATE_STUDENT_PAYMENT(Constants.CREATE_STUDENT_PAYMENT),
    V1_COLLEAGUE_UPDATE_STUDENT(Constants.UPDATE_STUDENT),
    V1_COLLEAGUE_CREATE_STUDENT_ENROLLMENT(Constants.CREATE_STUDENT_ENROLLMENT),
    V1_COLLEAGUE_UPDATE_STUDENT_ENROLLMENT(Constants.UPDATE_STUDENT_ENROLLMENT),
    V1_COLLEAGUE_UPDATE_PERSON(Constants.UPDATE_PERSON),
    V1_COLLEAGUE_UPDATE_PERSON_ADDRESS(Constants.UPDATE_PERSON_ADDRESS),
    V1_COLLEAGUE_GET_ACADEMIC_LEVELS(Constants.GET_ACADEMIC_LEVELS),
    V1_COLLEAGUE_GET_SUBJECTS(Constants.GET_SUBJECTS),
    V1_COLLEAGUE_GET_ROOMS(Constants.GET_ROOMS),
    V1_COLLEAGUE_GET_LOCATIONS(Constants.GET_LOCATIONS),
    V1_COLLEAGUE_CREATE_FINAL_GRADE(Constants.CREATE_FINAL_GRADE),
    V1_COLLEAGUE_UPDATE_FINAL_GRADE(Constants.UPDATE_FINAL_GRADE),
    V1_COLLEAGUE_GET_INSTRUCTIONAL_METHODS(Constants.GET_INSTRUCTIONAL_METHODS),
    V1_COLLEAGUE_GET_INSTRUCTORS(Constants.GET_INSTRUCTORS),
    V1_COLLEAGUE_GET_INSTRUCTOR(Constants.GET_INSTRUCTOR),
    V1_COLLEAGUE_GET_INSTRUCTOR_BY_ID(Constants.GET_INSTRUCTOR_BY_ID),
    V1_COLLEAGUE_CREATE_ORGANIZATION(Constants.CREATE_ORGANIZATION),
    V1_COLLEAGUE_UPDATE_ORGANIZATION(Constants.UPDATE_ORGANIZATION);

    String contextPath;

    String id;

    CoreColleagueRouteIds(String contextPath) {
        this.contextPath = contextPath;
        this.id = generateRouteId(new String[]{VERSION_1, COLLEAGUE_SYSTEM_ID, contextPath});
    }
}
