package org.moderncampus.integration.colleague.workflow.route.helper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.exception.ApplicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * Simple processor to validate that a person is an instructor for the new /instructors/id={personId} endpoint.
 * 
 * This processor executes BEFORE calling the PERSONS endpoint and validates that the requested person ID 
 * corresponds to an actual instructor. If not, it throws a 404 error.
 */
@Component
@Qualifier("instructorByIdValidationProcessor")
public class InstructorByIdValidationProcessor extends ColleagueEthosAssocResolver implements Processor {

    private static final Logger logger = LoggerFactory.getLogger(InstructorByIdValidationProcessor.class);

    public InstructorByIdValidationProcessor(ProducerTemplate producerTemplate, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator readEndpointPaginator) {
        super(producerTemplate, camelContext, readEndpointPaginator);
        logger.info("InstructorByIdValidationProcessor initialized successfully");
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        System.out.println("=== DEBUG: InstructorByIdValidationProcessor CALLED ===");

        // Extract person ID from the exchange - for the new route it will be in the path parameter
        String personId = extractPersonIdFromPath(exchange);

        System.out.println("DEBUG: Extracted person ID: " + personId);
        System.out.println("DEBUG: Exchange headers: " + exchange.getIn().getHeaders());

        logger.debug("Validating instructor existence for person ID: {}", personId);
        
        if (personId == null || personId.trim().isEmpty()) {
            throw new ApplicationException("Person ID is required", 400);
        }
        
        try {
            // Create a new exchange for the INSTRUCTORS endpoint call
            Exchange newExchange = exchange.copy();
            buildInstructorSearchCriteria(personId, newExchange);
            
            // Call the INSTRUCTORS endpoint to validate instructor existence
            List<Map<String, ?>> parsedAssocResponse = invokeEthosReadEndpoint(ColleagueEthosAPIResource.INSTRUCTORS, newExchange);
            
            if (parsedAssocResponse == null || parsedAssocResponse.isEmpty()) {
                String errorMessage = String.format("No instructor was found for id '%s'", personId);
                logger.warn("Instructor validation failed: {}", errorMessage);
                throw new ApplicationException(errorMessage, 404);
            }
            
            // Filter results to only include instructors that match the person ID
            List<Map<String, ?>> filteredResponse = parsedAssocResponse.stream()
                .filter(instructor -> personId.equals(getInstructorPersonId(instructor)))
                .collect(java.util.stream.Collectors.toList());
            
            if (filteredResponse.isEmpty()) {
                String errorMessage = String.format("No instructor was found for id '%s'", personId);
                logger.warn("Instructor validation failed: {}", errorMessage);
                throw new ApplicationException(errorMessage, 404);
            }
            
            // Set the instructor association data for use by other processors
            exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", filteredResponse);
            
            // Set the person ID in the body for the subsequent PERSONS call
            exchange.getIn().setBody(personId);
            
            logger.debug("Instructor validation successful for person ID: {}. Found {} instructor records.", 
                        personId, filteredResponse.size());
                        
        } catch (ApplicationException e) {
            // Re-throw ApplicationExceptions (our validation errors)
            throw e;
        } catch (Exception e) {
            // Log and re-throw other exceptions
            logger.error("Failed to validate instructor for person {}: {}", personId, e.getMessage(), e);
            throw new ApplicationException("Failed to validate instructor: " + e.getMessage(), 500);
        }
    }
    
    /**
     * Extract person ID from the path parameter for the new /instructors/id={personId} route
     */
    private String extractPersonIdFromPath(Exchange exchange) {
        System.out.println("DEBUG: Extracting person ID from exchange");

        // For the new route /instructors/id={personId}, the ID will be in the path
        String httpPath = exchange.getIn().getHeader("CamelHttpPath", String.class);
        String httpUri = exchange.getIn().getHeader("CamelHttpUri", String.class);
        String httpUrl = exchange.getIn().getHeader("CamelHttpUrl", String.class);

        System.out.println("DEBUG: CamelHttpPath = " + httpPath);
        System.out.println("DEBUG: CamelHttpUri = " + httpUri);
        System.out.println("DEBUG: CamelHttpUrl = " + httpUrl);

        // Try multiple approaches to extract the ID
        String personId = null;

        // Approach 1: From CamelHttpPath
        if (httpPath != null && httpPath.contains("id=")) {
            String[] parts = httpPath.split("id=");
            if (parts.length > 1) {
                personId = parts[1].trim();
                System.out.println("DEBUG: Extracted ID from CamelHttpPath: " + personId);
            }
        }

        // Approach 2: From CamelHttpUri
        if (personId == null && httpUri != null && httpUri.contains("id=")) {
            String[] parts = httpUri.split("id=");
            if (parts.length > 1) {
                personId = parts[1].trim();
                System.out.println("DEBUG: Extracted ID from CamelHttpUri: " + personId);
            }
        }

        // Approach 3: From CamelHttpUrl
        if (personId == null && httpUrl != null && httpUrl.contains("id=")) {
            String[] parts = httpUrl.split("id=");
            if (parts.length > 1) {
                personId = parts[1].trim();
                System.out.println("DEBUG: Extracted ID from CamelHttpUrl: " + personId);
            }
        }

        // Fallback: try to get from body if available
        if (personId == null) {
            personId = exchange.getIn().getBody(String.class);
            System.out.println("DEBUG: Extracted ID from body: " + personId);
        }

        System.out.println("DEBUG: Final extracted person ID: " + personId);
        return personId;
    }
    
    /**
     * Build search criteria for INSTRUCTORS endpoint using person ID
     */
    private void buildInstructorSearchCriteria(String personId, Exchange newExchange) {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        
        // Build criteria as specified in ticket: {"instructor":"<person-id>"}
        Map<String, Object> criteria = new HashMap<>();
        criteria.put("instructor", personId);
        
        entityRequest.setCriteria(criteria);
        newExchange.getMessage().setBody(entityRequest);
        
        logger.debug("Built instructor search criteria: {}", criteria);
    }
    
    /**
     * Extract person ID from instructor data structure
     */
    private String getInstructorPersonId(Map<String, ?> instructor) {
        // Extract person ID from instructor data
        // The instructor object should have a reference to the person
        Map<String, ?> instructorRef = (Map<String, ?>) instructor.get("instructor");
        if (instructorRef != null) {
            return (String) instructorRef.get("id");
        }
        return null;
    }
}
