package org.moderncampus.integration.colleague.workflow.route.helper;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;

import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.exception.ApplicationException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import groovy.json.JsonSlurper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

/**
 * Instructor association resolver specifically for GET-1 (get instructor by ID) endpoint.
 * This resolver validates that a person is actually an instructor before allowing the request to proceed.
 * 
 * Flow:
 * 1. Extract person ID from the request path
 * 2. Call INSTRUCTORS endpoint with criteria {"instructor":"<person-id>"}
 * 3. If empty array returned, throw error: "No instructor was found for id '<person-id>'"
 * 4. If records found, set instructor association data and allow request to proceed
 */
@Component
@Qualifier("instructorByIdAssocResolver")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosColleagueInstructorByIdAssocResolver extends ColleagueEthosAssocResolver implements Processor {

    private static final Logger logger = LoggerFactory.getLogger(EthosColleagueInstructorByIdAssocResolver.class);

    public EthosColleagueInstructorByIdAssocResolver(ProducerTemplate producerTemplate, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator readEndpointPaginator) {
        super(producerTemplate, camelContext, readEndpointPaginator);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        // Extract person ID from the exchange property set by the route builder
        String personId = extractPersonIdFromExchange(exchange);

        if (personId == null || personId.trim().isEmpty()) {
            throw new ApplicationException("Person ID is required", 400);
        }

        logger.debug("Validating instructor existence for person ID: {}", personId);

        try {
            // Build search criteria for INSTRUCTORS endpoint
            Exchange newExchange = exchange.copy();
            buildInstructorSearchCriteria(personId, newExchange);
            
            // Call INSTRUCTORS endpoint to validate instructor exists
            List<Map<String, ?>> parsedAssocResponse = invokeEthosReadEndpoint(ColleagueEthosAPIResource.INSTRUCTORS,
                    newExchange);

            // Filter results to only include instructors that match the person ID
            List<Map<String, ?>> filteredResponse = parsedAssocResponse.stream()
                .filter(instructor -> personId.equals(getInstructorPersonId(instructor)))
                .collect(java.util.stream.Collectors.toList());

            if (filteredResponse.isEmpty()) {
                // Person is not an instructor - throw specific error as required by ticket
                String errorMessage = String.format("No instructor was found for id '%s'", personId);
                logger.warn("Instructor validation failed: {}", errorMessage);
                throw new ApplicationException(errorMessage, 404);
            }

            // Person is a valid instructor - set association data for transform
            exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", filteredResponse);
            logger.debug("Instructor validation successful for person ID: {}, found {} instructor records", 
                        personId, filteredResponse.size());

        } catch (ApplicationException e) {
            // Re-throw ApplicationExceptions (our validation errors)
            throw e;
        } catch (Exception e) {
            // Log and re-throw other exceptions
            logger.error("Failed to validate instructor for person {}: {}", personId, e.getMessage(), e);
            throw new ApplicationException("Failed to validate instructor: " + e.getMessage(), 500);
        }
    }

    /**
     * Extract person ID from the exchange property set by the route builder
     */
    private String extractPersonIdFromExchange(Exchange exchange) {
        // The person ID is stored as RESOURCE_ID property by the GetByIdFromExtSystemRouteBuilder
        String personId = exchange.getProperty("resourceId", String.class);

        if (personId == null) {
            // Fallback to other common locations
            personId = exchange.getIn().getHeader("id", String.class);

            if (personId == null) {
                // Try to get it from the CamelHttpPath as last resort
                personId = exchange.getIn().getHeader("CamelHttpPath", String.class);
                if (personId != null && personId.contains("/")) {
                    // Extract the last segment which should be the ID
                    String[] pathSegments = personId.split("/");
                    personId = pathSegments[pathSegments.length - 1];
                }
            }
        }

        return personId;
    }

    /**
     * Build search criteria for INSTRUCTORS endpoint using person ID
     */
    private void buildInstructorSearchCriteria(String personId, Exchange newExchange) {
        SearchEntityRequest entityRequest = new SearchEntityRequest();

        // Build criteria as specified in ticket: {"instructor":"<person-id>"}
        Map<String, Object> criteria = Map.of("instructor", personId);

        entityRequest.setCriteria(criteria);
        newExchange.getMessage().setBody(entityRequest);

        logger.debug("Built instructor search criteria: {}", criteria);
    }

    /**
     * Extract person ID from instructor data structure
     */
    private String getInstructorPersonId(Map<String, ?> instructor) {
        // Extract person ID from instructor data
        // The instructor object should have a reference to the person
        Map<String, ?> instructorRef = (Map<String, ?>) instructor.get("instructor");
        if (instructorRef != null) {
            return (String) instructorRef.get("id");
        }
        return null;
    }
}
