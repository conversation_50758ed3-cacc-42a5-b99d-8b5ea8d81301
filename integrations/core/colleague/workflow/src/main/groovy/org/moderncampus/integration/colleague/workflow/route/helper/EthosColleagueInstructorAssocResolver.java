package org.moderncampus.integration.colleague.workflow.route.helper;


import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;

import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import groovy.json.JsonSlurper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Component
@Qualifier("instructorAssocResolver")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosColleagueInstructorAssocResolver extends ColleagueEthosAssocResolver implements Processor {

    private static final Logger logger = LoggerFactory.getLogger(EthosColleagueInstructorAssocResolver.class);

    public EthosColleagueInstructorAssocResolver(ProducerTemplate producerTemplate, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator readEndpointPaginator) {
        super(producerTemplate, camelContext, readEndpointPaginator);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        Exchange newExchange = exchange.copy();
        String personId = extractPersonIdFromEthosPersonResp(newExchange);
        if (personId != null) {
            try {
                buildInstructorSearchCriteria(personId, newExchange);
                List<Map<String, ?>> parsedAssocResponse = invokeEthosReadEndpoint(ColleagueEthosAPIResource.INSTRUCTORS,
                        newExchange);

                // Filter results to only include instructors that match the person ID
                List<Map<String, ?>> filteredResponse = parsedAssocResponse.stream()
                    .filter(instructor -> personId.equals(getInstructorPersonId(instructor)))
                    .collect(java.util.stream.Collectors.toList());

                if (!filteredResponse.isEmpty()) {
                    exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", filteredResponse);
                }
            } catch (Exception e) {
                // Log the error but don't fail the entire process
                // This allows the person data to still be returned even if instructor data fails
                logger.warn("Failed to fetch instructor data for person {}: {}", personId, e.getMessage());
            }
        }
    }

    private void buildInstructorSearchCriteria(String personId, Exchange newExchange) {
        // Use a simple search without complex criteria to avoid 405 errors
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        // Don't set complex criteria - let the endpoint return all instructors
        // We'll filter by person ID after getting the results
        newExchange.getMessage().setBody(entityRequest);
        // No additional search criteria needed - we filter results after retrieval
    }

    private String extractPersonIdFromEthosPersonResp(Exchange newExchange) {
        String body = newExchange.getMessage().getBody(String.class);
        Map<String, ?> parsedBody = (Map<String, ?>) new JsonSlurper().parseText(body);
        return (String) parsedBody.get("id");
    }

    private String getInstructorPersonId(Map<String, ?> instructor) {
        // Extract person ID from instructor data
        // The instructor object should have a reference to the person
        Map<String, ?> instructorRef = (Map<String, ?>) instructor.get("instructor");
        if (instructorRef != null) {
            return (String) instructorRef.get("id");
        }
        return null;
    }


}
