package org.moderncampus.integration.colleague.workflow.route.helper;


import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;

import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.exception.ApplicationException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import groovy.json.JsonSlurper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Component
@Qualifier("instructorAssocResolver")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosColleagueInstructorAssocResolver extends ColleagueEthosAssocResolver implements Processor {

    private static final Logger logger = LoggerFactory.getLogger(EthosColleagueInstructorAssocResolver.class);

    public EthosColleagueInstructorAssocResolver(ProducerTemplate producerTemplate, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator readEndpointPaginator) {
        super(producerTemplate, camelContext, readEndpointPaginator);
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        // Debug logging
        System.out.println("=== DEBUG: EthosColleagueInstructorAssocResolver ===");
        System.out.println("DEBUG: Exchange body = " + exchange.getIn().getBody());
        System.out.println("DEBUG: Exchange body class = " + (exchange.getIn().getBody() != null ? exchange.getIn().getBody().getClass() : "null"));

        // Check if this is a GET-by-ID request during preFetchAssociations phase
        boolean isPreFetch = isGetByIdPreFetchPhase(exchange);
        System.out.println("DEBUG: isGetByIdPreFetchPhase = " + isPreFetch);

        if (isPreFetch) {
            System.out.println("DEBUG: Calling validateInstructorForGetById");
            // For GET-by-ID requests, validate instructor BEFORE calling PERSONS
            validateInstructorForGetById(exchange);
        } else {
            System.out.println("DEBUG: Calling processInstructorAssociation");
            // For other requests or post-PERSONS processing, use the original logic
            processInstructorAssociation(exchange);
        }
        System.out.println("=== END DEBUG ===");
    }

    /**
     * Original logic for fetching instructor association data after PERSONS call
     */
    private void processInstructorAssociation(Exchange exchange) throws Exception {
        Exchange newExchange = exchange.copy();
        String personId = extractPersonIdFromEthosPersonResp(newExchange);
        if (personId != null) {
            try {
                buildInstructorSearchCriteria(personId, newExchange);
                List<Map<String, ?>> parsedAssocResponse = invokeEthosReadEndpoint(ColleagueEthosAPIResource.INSTRUCTORS,
                        newExchange);

                // Filter results to only include instructors that match the person ID
                List<Map<String, ?>> filteredResponse = parsedAssocResponse.stream()
                    .filter(instructor -> personId.equals(getInstructorPersonId(instructor)))
                    .collect(java.util.stream.Collectors.toList());

                if (!filteredResponse.isEmpty()) {
                    exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", filteredResponse);
                }
            } catch (Exception e) {
                // Log the error but don't fail the entire process
                // This allows the person data to still be returned even if instructor data fails
                logger.warn("Failed to fetch instructor data for person {}: {}", personId, e.getMessage());
            }
        }
    }

    private void buildInstructorSearchCriteria(String personId, Exchange newExchange) {
        // Use a simple search without complex criteria to avoid 405 errors
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        // Don't set complex criteria - let the endpoint return all instructors
        // We'll filter by person ID after getting the results
        newExchange.getMessage().setBody(entityRequest);
        // No additional search criteria needed - we filter results after retrieval
    }

    private String extractPersonIdFromEthosPersonResp(Exchange newExchange) {
        String body = newExchange.getMessage().getBody(String.class);
        Map<String, ?> parsedBody = (Map<String, ?>) new JsonSlurper().parseText(body);
        return (String) parsedBody.get("id");
    }

    private String getInstructorPersonId(Map<String, ?> instructor) {
        // Extract person ID from instructor data
        // The instructor object should have a reference to the person
        Map<String, ?> instructorRef = (Map<String, ?>) instructor.get("instructor");
        if (instructorRef != null) {
            return (String) instructorRef.get("id");
        }
        return null;
    }

    /**
     * Determine if this is a GET-by-ID request during preFetchAssociations phase
     */
    private boolean isGetByIdPreFetchPhase(Exchange exchange) {
        // During preFetchAssociations for GET-by-ID, the body contains just the person ID as a string
        // During post-PERSONS processing, the body contains JSON response from PERSONS
        Object body = exchange.getIn().getBody();

        if (body instanceof String) {
            String bodyStr = (String) body;
            // If it's a simple string (person ID) and not JSON, it's likely preFetchAssociations
            return bodyStr != null && !bodyStr.trim().isEmpty() && !bodyStr.trim().startsWith("{");
        }

        return false;
    }

    /**
     * Validate that the person is an instructor for GET-by-ID requests during preFetchAssociations
     */
    private void validateInstructorForGetById(Exchange exchange) throws Exception {
        // Extract person ID from the exchange body
        String personId = exchange.getIn().getBody(String.class);

        if (personId == null || personId.trim().isEmpty()) {
            throw new ApplicationException("Person ID is required", 400);
        }

        logger.debug("Validating instructor existence for person ID: {}", personId);

        try {
            // Create a new exchange for the INSTRUCTORS endpoint call
            Exchange newExchange = exchange.copy();
            buildInstructorSearchCriteria(personId, newExchange);

            // Call the INSTRUCTORS endpoint to validate instructor existence
            List<Map<String, ?>> parsedAssocResponse = invokeEthosReadEndpoint(ColleagueEthosAPIResource.INSTRUCTORS, newExchange);

            if (parsedAssocResponse == null || parsedAssocResponse.isEmpty()) {
                String errorMessage = String.format("No instructor was found for id '%s'", personId);
                logger.warn("Instructor validation failed: {}", errorMessage);
                throw new ApplicationException(errorMessage, 404);
            }

            // Filter results to only include instructors that match the person ID
            List<Map<String, ?>> filteredResponse = parsedAssocResponse.stream()
                .filter(instructor -> personId.equals(getInstructorPersonId(instructor)))
                .collect(java.util.stream.Collectors.toList());

            if (filteredResponse.isEmpty()) {
                String errorMessage = String.format("No instructor was found for id '%s'", personId);
                logger.warn("Instructor validation failed: {}", errorMessage);
                throw new ApplicationException(errorMessage, 404);
            }

            // Set the instructor association data for use by other processors
            exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", filteredResponse);

            logger.debug("Instructor validation successful for person ID: {}. Found {} instructor records.",
                        personId, filteredResponse.size());

        } catch (ApplicationException e) {
            // Re-throw ApplicationExceptions (our validation errors)
            throw e;
        } catch (Exception e) {
            // Log and re-throw other exceptions
            logger.error("Failed to validate instructor for person {}: {}", personId, e.getMessage(), e);
            throw new ApplicationException("Failed to validate instructor: " + e.getMessage(), 500);
        }
    }

}
