package org.moderncampus.integration.webservice.core.colleague.instructor;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.asString;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.makeRequest;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.matchingJsonPath;
import static com.github.tomakehurst.wiremock.client.WireMock.absent;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.moderncampus.integration.webservice.core.colleague.ColleagueIntegrationControllerTests;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

/**
 * Integration tests for Colleague Instructor endpoints.
 * These tests verify that the instructor endpoints are properly configured
 * and can handle basic requests with proper WireMock setup.
 */
@SpringBootTest
@TestPropertySource("/integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
public class ColleagueInstructorIntegrationControllerTests extends ColleagueIntegrationControllerTests {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    MockMvc mockMvc;

    @Value("${colleague.bearer.token}")
    private String COLLEAGUE_BEARER_TOKEN;

    @BeforeEach
    void initTest(
            @Value("classpath:colleague/ethos/EthosGetCourseTitleTypesResp.json") Resource ethosGetCourseTitleTypesResp,
            @Value("classpath:colleague/ethos/EthosGetAdminInstructionalMethodsResp.json") Resource ethosGetInsMethodsResp,
            @Value("classpath:colleague/ethos/EthosGetCreditCategoriesResp.json") Resource ethosGetCreditCategoriesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionDescriptionTypesResp.json") Resource ethosGetSectionDescriptionTypesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionTitleTypesResp.json") Resource ethosGetSectionTitleResp,
            @Value("classpath:colleague/ethos/EthosGetSectionsResp.json") Resource ethosGetSectionsResp,
            @Value("classpath:colleague/ethos/EthosGetSectionRegistrationStatusesResp.json") Resource ethosGetSectionRegistrationStatusesResp,
            @Value("classpath:colleague/ethos/EthosGetPersonNameTypesResp.json") Resource ethosGetPersonNameTypesResp,
            @Value("classpath:colleague/ethos/EthosGetEmailTypesResp.json") Resource ethosGetEmailTypesResp,
            @Value("classpath:colleague/ethos/EthosGetPhoneTypesResp.json") Resource ethosGetPhoneTypesResp,
            @Value("classpath:colleague/ethos/EthosGetAddressTypesResp.json") Resource ethosGetAddressTypesResp,
            @Value("classpath:colleague/ethos/EthosGetCitizenshipStatusesResp.json") Resource ethosGetCitizenshipStatusesResp,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactTypesResp.json") Resource ethosGetEmergencyContactTypesResp,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactPhoneResp.json") Resource ethosGetEmergencyContactPhoneResp
    ) throws Exception {

        // Setup common mock responses for instructor-related endpoints
        wireMockServer.stubFor(makeRequest(GET, ".*/course-title-types", ethosGetCourseTitleTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/instructional-methods", ethosGetInsMethodsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/credit-categories", ethosGetCreditCategoriesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-description-types", ethosGetSectionDescriptionTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-title-types", ethosGetSectionTitleResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/sections", ethosGetSectionsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatusesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/person-name-types", ethosGetPersonNameTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/email-types", ethosGetEmailTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/phone-types", ethosGetPhoneTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/address-types", ethosGetAddressTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/citizenship-statuses", ethosGetCitizenshipStatusesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-types", ethosGetEmergencyContactTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-phone-availabilities", ethosGetEmergencyContactPhoneResp));
    }

    @Test
    void colleagueGetInstructors(
            @Value("classpath:colleague/ethos/EthosGetPersonsResp.json") Resource ethosGetPersonsResp,
            @Value("classpath:colleague/ethos/EthosGetInstructorsResp.json") Resource ethosGetInstructorsResp,
            @Value("classpath:colleague/GetInstructorsResp.json") Resource getInstructorsResp
    ) throws Exception {

        // Mock the PERSONS endpoint call
        wireMockServer.stubFor(makeRequest(GET, "/api/persons.*", ethosGetPersonsResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v12.1.0+json")));

        // Mock the INSTRUCTORS endpoint call
        wireMockServer.stubFor(makeRequest(GET, "/api/instructors.*", ethosGetInstructorsResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v9.0.0+json")));

        mockMvc.perform(get("/uapi/integration/v1/instructors")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value("8c005e2e-14fd-47f3-a772-eb88705818ef"))
                .andExpect(jsonPath("$.data[0].names[0].firstName").value("John"))
                .andExpect(jsonPath("$.data[0].names[0].lastName").value("Smith"))
                .andExpect(jsonPath("$.data[0].status").value("A"))
                .andExpect(jsonPath("$.data[1].id").value("7b004d1d-13ec-46f2-a661-da77604817de"))
                .andExpect(jsonPath("$.data[1].names[0].firstName").value("Jane"))
                .andExpect(jsonPath("$.data[1].names[0].lastName").value("Doe"))
                .andExpect(jsonPath("$.data[1].status").value("A"))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        // Verify that both endpoints were called
        wireMockServer.verify(getRequestedFor(urlMatching("/api/persons.*")));
        wireMockServer.verify(getRequestedFor(urlMatching("/api/instructors.*")));
    }

    @Test
    void colleagueGetInstructorById(
            @Value("classpath:colleague/ethos/EthosGetPersonByIdResp.json") Resource ethosGetPersonByIdResp,
            @Value("classpath:colleague/ethos/EthosGetInstructorByIdResp.json") Resource ethosGetInstructorByIdResp,
            @Value("classpath:colleague/GetInstructorByIdResp.json") Resource getInstructorByIdResp
    ) throws Exception {

        String instructorId = "8c005e2e-14fd-47f3-a772-eb88705818ef";

        // Mock the INSTRUCTORS endpoint call first (new flow validation)
        wireMockServer.stubFor(makeRequest(GET, ".*/instructors\\?criteria=.*instructor.*" + instructorId + ".*", ethosGetInstructorByIdResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v9.0.0+json")));

        // Mock the PERSONS endpoint call for specific instructor
        wireMockServer.stubFor(makeRequest(GET, ".*/persons/" + instructorId, ethosGetPersonByIdResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v12.1.0+json")));

        mockMvc.perform(get("/uapi/integration/v1/instructors/" + instructorId)
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getInstructorByIdResp)));

        // Verify that both endpoints were called in the correct order
        wireMockServer.verify(getRequestedFor(urlMatching(".*/instructors\\?criteria=.*instructor.*" + instructorId + ".*")));
        wireMockServer.verify(getRequestedFor(urlMatching(".*/persons/" + instructorId)));
    }

    @Test
    void colleagueGetInstructorsWithPagination(
            @Value("classpath:colleague/ethos/EthosGetPersonsPaginatedResp.json") Resource ethosGetPersonsPaginatedResp,
            @Value("classpath:colleague/ethos/EthosGetInstructorsPaginatedResp.json") Resource ethosGetInstructorsPaginatedResp,
            @Value("classpath:colleague/GetInstructorsPaginatedResp.json") Resource getInstructorsPaginatedResp
    ) throws Exception {

        // Mock the PERSONS endpoint call with criteria for instructor role
        wireMockServer.stubFor(makeRequest(GET, ".*/persons\\?criteria=.*roles.*instructor.*", ethosGetPersonsPaginatedResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v12.1.0+json")));

        // Mock the INSTRUCTORS endpoint call with pagination
        wireMockServer.stubFor(makeRequest(GET, ".*/instructors.*", ethosGetInstructorsPaginatedResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v9.0.0+json")));

        mockMvc.perform(get("/uapi/integration/v1/instructors")
                        .param("offset", "0")
                        .param("limit", "10")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getInstructorsPaginatedResp)));

        // Verify that both endpoints were called
        wireMockServer.verify(getRequestedFor(urlMatching(".*/persons\\?criteria=.*roles.*instructor.*")));
        wireMockServer.verify(getRequestedFor(urlMatching(".*/instructors.*")));
    }

    @Test
    void colleagueGetInstructorsWithFilters(
            @Value("classpath:colleague/ethos/EthosGetPersonsFilteredResp.json") Resource ethosGetPersonsFilteredResp,
            @Value("classpath:colleague/ethos/EthosGetInstructorsFilteredResp.json") Resource ethosGetInstructorsFilteredResp,
            @Value("classpath:colleague/GetInstructorsFilteredResp.json") Resource getInstructorsFilteredResp
    ) throws Exception {

        // Mock the PERSONS endpoint call with filters
        wireMockServer.stubFor(makeRequest(GET, ".*/persons.*criteria.*", ethosGetPersonsFilteredResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v12.1.0+json")));



        mockMvc.perform(get("/uapi/integration/v1/instructors")
                        .param("firstName", "John")
                        .param("lastName", "Doe")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getInstructorsFilteredResp)));

        // Verify that the PERSONS endpoint was called
        wireMockServer.verify(getRequestedFor(urlMatching(".*/persons.*criteria.*")));
    }

    @Test
    void colleagueGetInstructorsNotFound(
            @Value("classpath:colleague/ethos/EthosResourceEmptyResp.json") Resource ethosEmptyResp
    ) throws Exception {

        // Mock empty response from PERSONS endpoint with criteria for instructor role
        wireMockServer.stubFor(makeRequest(GET, ".*/persons\\?criteria=.*roles.*instructor.*", ethosEmptyResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v12.1.0+json")));

        mockMvc.perform(get("/uapi/integration/v1/instructors")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isEmpty())
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void colleagueGetInstructorByIdNotFound() throws Exception {

        String nonExistentId = "00000000-0000-0000-0000-000000000000";

        // Mock 404 response from PERSONS endpoint
        wireMockServer.stubFor(WireMock.get(urlMatching(".*/persons/" + nonExistentId))
                .willReturn(aResponse()
                        .withStatus(404)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"errors\":[{\"code\":\"Global.Internal.NotFound\",\"message\":\"Person not found\"}]}")));

        mockMvc.perform(get("/uapi/integration/v1/instructors/" + nonExistentId)
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isNotFound());

        // Verify that PERSONS endpoint was called
        wireMockServer.verify(getRequestedFor(urlMatching(".*/persons/" + nonExistentId)));
    }
}
